{% extends "base.html" %}

{% block title %}تعديل المورد - نظام إدارة الصيدلية{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>تعديل المورد: {{ supplier.name }}
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم المورد *</label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="{{ supplier.name }}" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="{{ supplier.phone or '' }}" placeholder="05xxxxxxxx">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="{{ supplier.email or '' }}" placeholder="<EMAIL>">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="3"
                                  placeholder="العنوان الكامل للمورد أو الشركة...">{{ supplier.address or '' }}</textarea>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('suppliers') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>العودة
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ التعديلات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- معلومات المورد الحالية -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card bg-light">
            <div class="card-header">
                <h6 class="mb-0">معلومات المورد الحالية</h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>تاريخ التسجيل:</strong></td>
                        <td>{{ supplier.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                    </tr>
                    <tr>
                        <td><strong>عدد الأدوية:</strong></td>
                        <td>{{ supplier.medicines|length }}</td>
                    </tr>
                    <tr>
                        <td><strong>قيمة المخزون:</strong></td>
                        <td>
                            {% set total_value = 0 %}
                            {% for medicine in supplier.medicines %}
                                {% set total_value = total_value + (medicine.price * medicine.quantity) %}
                            {% endfor %}
                            {{ "%.2f"|format(total_value) }} ريال
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card bg-light">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-lightbulb text-warning me-2"></i>نصائح التعديل
                </h6>
                <ul class="mb-0">
                    <li>تأكد من صحة البيانات قبل الحفظ</li>
                    <li>رقم الهاتف مفيد للتواصل مع المورد</li>
                    <li>البريد الإلكتروني يمكن استخدامه للمراسلات التجارية</li>
                    <li>العنوان مفيد لمعرفة موقع المورد</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- الأدوية المرتبطة بالمورد -->
{% if supplier.medicines %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">الأدوية المرتبطة بهذا المورد</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>اسم الدواء</th>
                                <th>الفئة</th>
                                <th>السعر</th>
                                <th>الكمية</th>
                                <th>تاريخ الانتهاء</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for medicine in supplier.medicines[:10] %}
                            <tr>
                                <td>{{ medicine.name }}</td>
                                <td><span class="badge bg-info">{{ medicine.category }}</span></td>
                                <td>{{ "%.2f"|format(medicine.price) }} ريال</td>
                                <td>
                                    {% if medicine.quantity <= 10 %}
                                        <span class="badge bg-warning">{{ medicine.quantity }}</span>
                                    {% else %}
                                        <span class="badge bg-success">{{ medicine.quantity }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ medicine.expiry_date.strftime('%Y-%m-%d') }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% if supplier.medicines|length > 10 %}
                    <p class="text-muted text-center">
                        وهناك {{ supplier.medicines|length - 10 }} أدوية أخرى...
                    </p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من صحة رقم الهاتف السعودي
    const phoneInput = document.getElementById('phone');
    phoneInput.addEventListener('input', function() {
        const phone = this.value;
        if (phone && !phone.match(/^05\d{8}$/)) {
            this.setCustomValidity('رقم الهاتف يجب أن يبدأ بـ 05 ويتكون من 10 أرقام');
        } else {
            this.setCustomValidity('');
        }
    });
    
    // التحقق من صحة البريد الإلكتروني
    const emailInput = document.getElementById('email');
    emailInput.addEventListener('input', function() {
        const email = this.value;
        if (email && !email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
            this.setCustomValidity('البريد الإلكتروني غير صحيح');
        } else {
            this.setCustomValidity('');
        }
    });
});
</script>
{% endblock %}