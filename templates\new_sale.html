{% extends "base.html" %}

{% block title %}فاتورة جديدة - نظام إدارة الصيدلية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-plus-circle me-2"></i>إنشاء فاتورة جديدة
                </h5>
            </div>
            <div class="card-body">
                <form id="saleForm" method="POST">
                    <!-- معلومات العميل -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="customer_id" class="form-label">العميل (اختياري)</label>
                            <select class="form-select" id="customer_id" name="customer_id">
                                <option value="">عميل غير مسجل</option>
                                {% for customer in customers %}
                                <option value="{{ customer.id }}">{{ customer.name }} - {{ customer.phone or 'بدون هاتف' }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="sale_date" class="form-label">تاريخ الفاتورة</label>
                            <input type="datetime-local" class="form-control" id="sale_date" name="sale_date" readonly>
                        </div>
                    </div>

                    <!-- البحث عن الأدوية -->
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <label for="medicine_search" class="form-label">البحث عن دواء</label>
                            <input type="text" class="form-control" id="medicine_search" 
                                   placeholder="ابحث عن الدواء بالاسم...">
                            <div id="medicine_suggestions" class="list-group mt-1" style="display: none;"></div>
                        </div>
                        <div class="col-md-4">
                            <label for="quantity_input" class="form-label">الكمية</label>
                            <input type="number" class="form-control" id="quantity_input" 
                                   min="1" value="1">
                        </div>
                    </div>

                    <!-- جدول الأصناف المضافة -->
                    <div class="table-responsive mb-4">
                        <table class="table table-bordered" id="items_table">
                            <thead class="table-primary">
                                <tr>
                                    <th>الدواء</th>
                                    <th>السعر</th>
                                    <th>الكمية</th>
                                    <th>الإجمالي</th>
                                    <th>إجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="items_tbody">
                                <tr id="empty_row">
                                    <td colspan="5" class="text-center text-muted">لم يتم إضافة أي أصناف بعد</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- ملخص الفاتورة -->
                    <div class="row">
                        <div class="col-md-8"></div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <table class="table table-sm mb-0">
                                        <tr>
                                            <td><strong>إجمالي المبلغ:</strong></td>
                                            <td class="text-end"><span id="total_amount">0.00</span> ريال</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <label for="discount">الخصم:</label>
                                                <input type="number" class="form-control form-control-sm" 
                                                       id="discount" name="discount" min="0" step="0.01" value="0">
                                            </td>
                                            <td class="text-end"><span id="discount_amount">0.00</span> ريال</td>
                                        </tr>
                                        <tr class="table-success">
                                            <td><strong>المبلغ النهائي:</strong></td>
                                            <td class="text-end"><strong><span id="final_amount">0.00</span> ريال</strong></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="d-flex justify-content-between mt-4">
                        <a href="{{ url_for('sales') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>العودة
                        </a>
                        <button type="submit" class="btn btn-success" id="save_btn" disabled>
                            <i class="fas fa-save me-2"></i>حفظ الفاتورة
                        </button>
                    </div>

                    <!-- حقل مخفي للأصناف -->
                    <input type="hidden" name="items" id="items_data">
                </form>
            </div>
        </div>
    </div>
</div>

<!-- نصائح -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card bg-light">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-lightbulb text-warning me-2"></i>نصائح الاستخدام
                </h6>
                <ul class="mb-0">
                    <li>ابحث عن الدواء بكتابة اسمه في حقل البحث</li>
                    <li>اختر الكمية المطلوبة ثم اضغط على الدواء لإضافته</li>
                    <li>يمكنك تعديل الكمية مباشرة في الجدول</li>
                    <li>يمكن إضافة خصم على إجمالي الفاتورة</li>
                    <li>اختيار العميل اختياري ولكنه مفيد للتتبع</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let saleItems = [];
let medicines = [];

// تحميل الأدوية عند بدء الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تعيين التاريخ والوقت الحالي
    const now = new Date();
    const dateTimeString = now.getFullYear() + '-' + 
        String(now.getMonth() + 1).padStart(2, '0') + '-' + 
        String(now.getDate()).padStart(2, '0') + 'T' + 
        String(now.getHours()).padStart(2, '0') + ':' + 
        String(now.getMinutes()).padStart(2, '0');
    document.getElementById('sale_date').value = dateTimeString;
    
    loadMedicines();
    
    // إعداد البحث
    const searchInput = document.getElementById('medicine_search');
    searchInput.addEventListener('input', searchMedicines);
    
    // إعداد حساب الخصم
    const discountInput = document.getElementById('discount');
    discountInput.addEventListener('input', calculateTotal);
});

// تحميل الأدوية من الخادم
function loadMedicines() {
    medicines = {{ medicines|tojson }};
}

// البحث في الأدوية
function searchMedicines() {
    const query = document.getElementById('medicine_search').value.toLowerCase();
    const suggestions = document.getElementById('medicine_suggestions');
    
    if (query.length < 2) {
        suggestions.style.display = 'none';
        return;
    }
    
    const filtered = medicines.filter(medicine => 
        medicine.name.toLowerCase().includes(query) && medicine.quantity > 0
    );
    
    suggestions.innerHTML = '';
    
    if (filtered.length > 0) {
        filtered.slice(0, 5).forEach(medicine => {
            const item = document.createElement('a');
            item.className = 'list-group-item list-group-item-action';
            item.innerHTML = `
                <div class="d-flex justify-content-between">
                    <span>${medicine.name}</span>
                    <span class="text-muted">${medicine.price.toFixed(2)} ريال - متوفر: ${medicine.quantity}</span>
                </div>
            `;
            item.onclick = () => addMedicine(medicine);
            suggestions.appendChild(item);
        });
        suggestions.style.display = 'block';
    } else {
        suggestions.innerHTML = '<div class="list-group-item text-muted">لا توجد نتائج</div>';
        suggestions.style.display = 'block';
    }
}

// إضافة دواء للفاتورة
function addMedicine(medicine) {
    const quantity = parseInt(document.getElementById('quantity_input').value);
    
    if (quantity <= 0) {
        alert('الكمية يجب أن تكون أكبر من صفر');
        return;
    }
    
    if (quantity > medicine.quantity) {
        alert(`الكمية المطلوبة (${quantity}) أكبر من المتوفر (${medicine.quantity})`);
        return;
    }
    
    // التحقق من وجود الدواء مسبقاً
    const existingIndex = saleItems.findIndex(item => item.medicine_id === medicine.id);
    
    if (existingIndex !== -1) {
        const newQuantity = saleItems[existingIndex].quantity + quantity;
        if (newQuantity > medicine.quantity) {
            alert(`إجمالي الكمية (${newQuantity}) أكبر من المتوفر (${medicine.quantity})`);
            return;
        }
        saleItems[existingIndex].quantity = newQuantity;
        saleItems[existingIndex].total_price = saleItems[existingIndex].quantity * medicine.price;
    } else {
        saleItems.push({
            medicine_id: medicine.id,
            medicine_name: medicine.name,
            unit_price: medicine.price,
            quantity: quantity,
            total_price: quantity * medicine.price,
            available_quantity: medicine.quantity
        });
    }
    
    updateItemsTable();
    calculateTotal();
    
    // إخفاء الاقتراحات وإعادة تعيين البحث
    document.getElementById('medicine_suggestions').style.display = 'none';
    document.getElementById('medicine_search').value = '';
    document.getElementById('quantity_input').value = '1';
}

// تحديث جدول الأصناف
function updateItemsTable() {
    const tbody = document.getElementById('items_tbody');
    const emptyRow = document.getElementById('empty_row');
    
    if (saleItems.length === 0) {
        emptyRow.style.display = 'table-row';
        document.getElementById('save_btn').disabled = true;
        return;
    }
    
    emptyRow.style.display = 'none';
    document.getElementById('save_btn').disabled = false;
    
    // إزالة الصفوف الموجودة (عدا الصف الفارغ)
    const existingRows = tbody.querySelectorAll('tr:not(#empty_row)');
    existingRows.forEach(row => row.remove());
    
    // إضافة الصفوف الجديدة
    saleItems.forEach((item, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${item.medicine_name}</td>
            <td>${item.unit_price.toFixed(2)} ريال</td>
            <td>
                <input type="number" class="form-control form-control-sm" 
                       value="${item.quantity}" min="1" max="${item.available_quantity}"
                       onchange="updateQuantity(${index}, this.value)">
            </td>
            <td>${item.total_price.toFixed(2)} ريال</td>
            <td>
                <button type="button" class="btn btn-sm btn-danger" onclick="removeItem(${index})">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// تحديث كمية صنف
function updateQuantity(index, newQuantity) {
    newQuantity = parseInt(newQuantity);
    
    if (newQuantity <= 0) {
        removeItem(index);
        return;
    }
    
    if (newQuantity > saleItems[index].available_quantity) {
        alert(`الكمية المطلوبة أكبر من المتوفر (${saleItems[index].available_quantity})`);
        updateItemsTable(); // إعادة تعيين القيمة
        return;
    }
    
    saleItems[index].quantity = newQuantity;
    saleItems[index].total_price = newQuantity * saleItems[index].unit_price;
    
    updateItemsTable();
    calculateTotal();
}

// حذف صنف
function removeItem(index) {
    saleItems.splice(index, 1);
    updateItemsTable();
    calculateTotal();
}

// حساب الإجمالي
function calculateTotal() {
    const total = saleItems.reduce((sum, item) => sum + item.total_price, 0);
    const discount = parseFloat(document.getElementById('discount').value) || 0;
    const final = total - discount;
    
    document.getElementById('total_amount').textContent = total.toFixed(2);
    document.getElementById('discount_amount').textContent = discount.toFixed(2);
    document.getElementById('final_amount').textContent = final.toFixed(2);
}

// إرسال النموذج
document.getElementById('saleForm').addEventListener('submit', function(e) {
    if (saleItems.length === 0) {
        e.preventDefault();
        alert('يجب إضافة صنف واحد على الأقل');
        return;
    }
    
    // تحضير بيانات الأصناف
    const itemsData = saleItems.map(item => ({
        medicine_id: item.medicine_id,
        quantity: item.quantity
    }));
    
    document.getElementById('items_data').value = JSON.stringify(itemsData);
});

// إخفاء الاقتراحات عند النقر خارجها
document.addEventListener('click', function(e) {
    if (!e.target.closest('#medicine_search') && !e.target.closest('#medicine_suggestions')) {
        document.getElementById('medicine_suggestions').style.display = 'none';
    }
});
</script>
{% endblock %}