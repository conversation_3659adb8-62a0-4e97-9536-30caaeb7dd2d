{% extends "base.html" %}

{% block title %}تقرير المخزون - نظام إدارة الصيدلية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-boxes me-2"></i>تقرير المخزون
                    {% if filter_type %}
                        - 
                        {% if filter_type == 'low_stock' %}أدوية قليلة المخزون
                        {% elif filter_type == 'expired' %}أدوية منتهية الصلاحية
                        {% elif filter_type == 'expiring' %}أدوية قريبة من الانتهاء
                        {% endif %}
                    {% endif %}
                </h5>
                <div>
                    <button onclick="window.print()" class="btn btn-success">
                        <i class="fas fa-print me-2"></i>طباعة التقرير
                    </button>
                    <a href="{{ url_for('reports') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>العودة
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- فلاتر التقرير -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="btn-group" role="group">
                            <a href="{{ url_for('inventory_report') }}" 
                               class="btn {{ 'btn-primary' if not filter_type else 'btn-outline-primary' }}">
                                جميع الأدوية
                            </a>
                            <a href="{{ url_for('inventory_report', filter='low_stock') }}" 
                               class="btn {{ 'btn-warning' if filter_type == 'low_stock' else 'btn-outline-warning' }}">
                                قليلة المخزون
                            </a>
                            <a href="{{ url_for('inventory_report', filter='expired') }}" 
                               class="btn {{ 'btn-danger' if filter_type == 'expired' else 'btn-outline-danger' }}">
                                منتهية الصلاحية
                            </a>
                            <a href="{{ url_for('inventory_report', filter='expiring') }}" 
                               class="btn {{ 'btn-warning' if filter_type == 'expiring' else 'btn-outline-warning' }}">
                                قريبة من الانتهاء
                            </a>
                        </div>
                    </div>
                </div>

                <!-- ملخص التقرير -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-pills fa-2x mb-2"></i>
                                <h4>{{ medicines|length }}</h4>
                                <small>عدد الأدوية</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-boxes fa-2x mb-2"></i>
                                <h4>{{ medicines|sum(attribute='quantity') }}</h4>
                                <small>إجمالي الكمية</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-money-bill fa-2x mb-2"></i>
                                <h4>{{ "%.2f"|format(total_value) }}</h4>
                                <small>قيمة المخزون (ريال)</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-dark">
                            <div class="card-body text-center">
                                <i class="fas fa-chart-pie fa-2x mb-2"></i>
                                <h4>{{ medicines|groupby('category')|list|length }}</h4>
                                <small>عدد الفئات</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول الأدوية -->
                {% if medicines %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>اسم الدواء</th>
                                <th>الفئة</th>
                                <th>المورد</th>
                                <th>السعر</th>
                                <th>الكمية</th>
                                <th>قيمة المخزون</th>
                                <th>تاريخ الانتهاء</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for medicine in medicines %}
                            <tr class="{% if medicine.quantity <= 10 %}table-warning{% endif %} 
                                       {% if medicine.expiry_date <= (moment().date() + timedelta(days=30)) %}table-danger{% endif %}">
                                <td>{{ medicine.id }}</td>
                                <td>
                                    <strong>{{ medicine.name }}</strong>
                                    {% if medicine.description %}
                                        <br><small class="text-muted">{{ medicine.description[:50] }}...</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ medicine.category }}</span>
                                </td>
                                <td>{{ medicine.supplier.name if medicine.supplier else 'غير محدد' }}</td>
                                <td>{{ "%.2f"|format(medicine.price) }} ريال</td>
                                <td>
                                    {% if medicine.quantity <= 10 %}
                                        <span class="badge bg-warning">{{ medicine.quantity }}</span>
                                    {% elif medicine.quantity <= 0 %}
                                        <span class="badge bg-danger">نفد المخزون</span>
                                    {% else %}
                                        <span class="badge bg-success">{{ medicine.quantity }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ "%.2f"|format(medicine.price * medicine.quantity) }} ريال</td>
                                <td>{{ medicine.expiry_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% set days_to_expiry = (medicine.expiry_date - moment().date()).days %}
                                    {% if days_to_expiry <= 0 %}
                                        <span class="badge bg-danger">منتهي</span>
                                    {% elif days_to_expiry <= 30 %}
                                        <span class="badge bg-warning">{{ days_to_expiry }} يوم</span>
                                    {% elif medicine.quantity <= 10 %}
                                        <span class="badge bg-warning">قليل</span>
                                    {% else %}
                                        <span class="badge bg-success">جيد</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot class="table-dark">
                            <tr>
                                <th colspan="5">الإجمالي</th>
                                <th>
                                    {% set total_quantity = 0 %}
                                    {% for medicine in medicines %}
                                        {% set total_quantity = total_quantity + medicine.quantity %}
                                    {% endfor %}
                                    {{ total_quantity }}
                                </th>
                                <th>{{ "%.2f"|format(total_value) }} ريال</th>
                                <th colspan="2"></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد أدوية تطابق الفلتر المحدد</h5>
                    <p class="text-muted">جرب تغيير الفلتر أو إضافة أدوية جديدة</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- تحليل إضافي -->
{% if medicines %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">الأدوية حسب الفئة</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>الفئة</th>
                                <th>عدد الأدوية</th>
                                <th>إجمالي الكمية</th>
                                <th>القيمة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for category, items in medicines|groupby('category') %}
                            {% set category_medicines = items|list %}
                            <tr>
                                <td>{{ category }}</td>
                                <td>{{ category_medicines|length }}</td>
                                <td>
                                    {% set cat_quantity = 0 %}
                                    {% for med in category_medicines %}
                                        {% set cat_quantity = cat_quantity + med.quantity %}
                                    {% endfor %}
                                    {{ cat_quantity }}
                                </td>
                                <td>
                                    {% set cat_value = 0 %}
                                    {% for med in category_medicines %}
                                        {% set cat_value = cat_value + (med.price * med.quantity) %}
                                    {% endfor %}
                                    {{ "%.2f"|format(cat_value) }} ريال
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">الموردين حسب عدد الأدوية</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>المورد</th>
                                <th>عدد الأدوية</th>
                                <th>إجمالي الكمية</th>
                                <th>القيمة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for supplier, items in medicines|groupby('supplier.name') %}
                            {% set supplier_medicines = items|list %}
                            <tr>
                                <td>{{ supplier or 'غير محدد' }}</td>
                                <td>{{ supplier_medicines|length }}</td>
                                <td>
                                    {% set sup_quantity = 0 %}
                                    {% for med in supplier_medicines %}
                                        {% set sup_quantity = sup_quantity + med.quantity %}
                                    {% endfor %}
                                    {{ sup_quantity }}
                                </td>
                                <td>
                                    {% set sup_value = 0 %}
                                    {% for med in supplier_medicines %}
                                        {% set sup_value = sup_value + (med.price * med.quantity) %}
                                    {% endfor %}
                                    {{ "%.2f"|format(sup_value) }} ريال
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- تنبيهات مهمة -->
<div class="row mt-4">
    <div class="col-md-4">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h6 class="mb-0">أدوية منتهية الصلاحية</h6>
            </div>
            <div class="card-body">
                {% set expired_medicines = medicines|selectattr('expiry_date', 'lt', moment().date())|list %}
                <h4 class="text-danger">{{ expired_medicines|length }}</h4>
                <p class="mb-0">يجب إزالتها من المخزون</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">أدوية قليلة المخزون</h6>
            </div>
            <div class="card-body">
                {% set low_stock_medicines = medicines|selectattr('quantity', 'le', 10)|list %}
                <h4 class="text-warning">{{ low_stock_medicines|length }}</h4>
                <p class="mb-0">تحتاج إعادة طلب</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">أدوية قريبة من الانتهاء</h6>
            </div>
            <div class="card-body">
                {% set expiring_medicines = medicines|selectattr('expiry_date', 'le', moment().date() + timedelta(days=30))|selectattr('expiry_date', 'ge', moment().date())|list %}
                <h4 class="text-info">{{ expiring_medicines|length }}</h4>
                <p class="mb-0">خلال 30 يوم</p>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
// تخصيص الطباعة
window.addEventListener('beforeprint', function() {
    document.body.classList.add('printing');
});

window.addEventListener('afterprint', function() {
    document.body.classList.remove('printing');
});
</script>

<style>
@media print {
    .btn, .card-header .btn, .btn-group {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .main-content {
        margin: 0 !important;
        padding: 0 !important;
        background: white !important;
    }
    
    body {
        background: white !important;
    }
    
    .table-responsive {
        overflow: visible !important;
    }
}
</style>
{% endblock %}