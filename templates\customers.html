{% extends "base.html" %}

{% block title %}إدارة العملاء - نظام إدارة الصيدلية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>إدارة العملاء
                </h5>
                <a href="{{ url_for('add_customer') }}" class="btn btn-primary">
                    <i class="fas fa-user-plus me-2"></i>إضافة عميل جديد
                </a>
            </div>
            <div class="card-body">
                <!-- شريط البحث -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <form method="GET" class="d-flex">
                            <input type="text" name="search" class="form-control me-2" 
                                   placeholder="البحث عن عميل..." value="{{ search }}">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                    </div>
                </div>

                <!-- جدول العملاء -->
                {% if customers.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>اسم العميل</th>
                                <th>رقم الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>العنوان</th>
                                <th>تاريخ التسجيل</th>
                                <th>عدد المشتريات</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for customer in customers.items %}
                            <tr>
                                <td>{{ customer.id }}</td>
                                <td>
                                    <strong>{{ customer.name }}</strong>
                                </td>
                                <td>
                                    {% if customer.phone %}
                                        <a href="tel:{{ customer.phone }}" class="text-decoration-none">
                                            <i class="fas fa-phone me-1"></i>{{ customer.phone }}
                                        </a>
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if customer.email %}
                                        <a href="mailto:{{ customer.email }}" class="text-decoration-none">
                                            <i class="fas fa-envelope me-1"></i>{{ customer.email }}
                                        </a>
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if customer.address %}
                                        {{ customer.address[:30] }}{% if customer.address|length > 30 %}...{% endif %}
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>{{ customer.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <span class="badge bg-info">{{ customer.sales|length }}</span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-info" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#customerModal{{ customer.id }}">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <a href="{{ url_for('edit_customer', id=customer.id) }}" 
                                           class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ url_for('delete_customer', id=customer.id) }}" 
                                           class="btn btn-sm btn-danger"
                                           onclick="return confirm('هل أنت متأكد من حذف هذا العميل؟')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>

                            <!-- Modal لعرض تفاصيل العميل -->
                            <div class="modal fade" id="customerModal{{ customer.id }}" tabindex="-1">
                                <div class="modal-dialog modal-lg">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">تفاصيل العميل: {{ customer.name }}</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <h6>المعلومات الأساسية</h6>
                                                    <table class="table table-sm">
                                                        <tr>
                                                            <td><strong>الاسم:</strong></td>
                                                            <td>{{ customer.name }}</td>
                                                        </tr>
                                                        <tr>
                                                            <td><strong>الهاتف:</strong></td>
                                                            <td>{{ customer.phone or 'غير محدد' }}</td>
                                                        </tr>
                                                        <tr>
                                                            <td><strong>البريد:</strong></td>
                                                            <td>{{ customer.email or 'غير محدد' }}</td>
                                                        </tr>
                                                        <tr>
                                                            <td><strong>العنوان:</strong></td>
                                                            <td>{{ customer.address or 'غير محدد' }}</td>
                                                        </tr>
                                                        <tr>
                                                            <td><strong>تاريخ التسجيل:</strong></td>
                                                            <td>{{ customer.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                                        </tr>
                                                    </table>
                                                </div>
                                                <div class="col-md-6">
                                                    <h6>إحصائيات المشتريات</h6>
                                                    {% set total_purchases = customer.sales|length %}
                                                    {% set total_amount = 0 %}
                                                    {% for sale in customer.sales %}
                                                        {% set total_amount = total_amount + sale.final_amount %}
                                                    {% endfor %}
                                                    <table class="table table-sm">
                                                        <tr>
                                                            <td><strong>عدد المشتريات:</strong></td>
                                                            <td><span class="badge bg-primary">{{ total_purchases }}</span></td>
                                                        </tr>
                                                        <tr>
                                                            <td><strong>إجمالي المبلغ:</strong></td>
                                                            <td><span class="badge bg-success">{{ "%.2f"|format(total_amount) }} ريال</span></td>
                                                        </tr>
                                                        <tr>
                                                            <td><strong>متوسط الفاتورة:</strong></td>
                                                            <td>{{ "%.2f"|format(total_amount / total_purchases if total_purchases > 0 else 0) }} ريال</td>
                                                        </tr>
                                                    </table>
                                                </div>
                                            </div>
                                            
                                            {% if customer.sales %}
                                            <hr>
                                            <h6>آخر المشتريات</h6>
                                            <div class="table-responsive">
                                                <table class="table table-sm">
                                                    <thead>
                                                        <tr>
                                                            <th>رقم الفاتورة</th>
                                                            <th>التاريخ</th>
                                                            <th>المبلغ</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {% for sale in customer.sales[-5:] %}
                                                        <tr>
                                                            <td>#{{ sale.id }}</td>
                                                            <td>{{ sale.sale_date.strftime('%Y-%m-%d') }}</td>
                                                            <td>{{ "%.2f"|format(sale.final_amount) }} ريال</td>
                                                        </tr>
                                                        {% endfor %}
                                                    </tbody>
                                                </table>
                                            </div>
                                            {% endif %}
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                            <a href="{{ url_for('edit_customer', id=customer.id) }}" class="btn btn-primary">تعديل</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if customers.pages > 1 %}
                <nav aria-label="صفحات العملاء">
                    <ul class="pagination justify-content-center">
                        {% if customers.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('customers', page=customers.prev_num, search=search) }}">السابق</a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in customers.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != customers.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('customers', page=page_num, search=search) }}">{{ page_num }}</a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if customers.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('customers', page=customers.next_num, search=search) }}">التالي</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا يوجد عملاء</h5>
                    <p class="text-muted">ابدأ بإضافة أول عميل في النظام</p>
                    <a href="{{ url_for('add_customer') }}" class="btn btn-primary">
                        <i class="fas fa-user-plus me-2"></i>إضافة عميل جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات العملاء -->
<div class="row mt-4">
    <div class="col-md-4">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x mb-2"></i>
                <h6>إجمالي العملاء</h6>
                <h4>{{ customers.total }}</h4>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-user-check fa-2x mb-2"></i>
                <h6>عملاء نشطون</h6>
                <h4>{{ customers.items|selectattr('sales')|list|length }}</h4>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-user-plus fa-2x mb-2"></i>
                <h6>عملاء جدد هذا الشهر</h6>
                <h4>{{ customers.items|selectattr('created_at')|selectattr('created_at', 'ge', moment().replace(day=1))|list|length }}</h4>
            </div>
        </div>
    </div>
</div>
{% endblock %}