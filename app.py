from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import Numeric, func
from datetime import datetime, date, timedelta
from config import Config
import json
from decimal import Decimal
 
app = Flask(__name__)
app.config.from_object(Config)

db = SQLAlchemy(app)

# دالة مساعدة لحساب الأيام المتبقية
def days_until_expiry(expiry_date):
    if not expiry_date:
        return None
    today = date.today()
    return (expiry_date - today).days

# إضافة الدالة للقوالب
app.jinja_env.globals.update(days_until_expiry=days_until_expiry)

# نماذج قاعدة البيانات
class Medicine(db.Model):
    __tablename__ = 'medicines'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    category = db.Column(db.String(100), nullable=False)
    price = db.Column(db.Float, nullable=False)
    quantity = db.Column(db.Integer, nullable=False, default=0)
    expiry_date = db.Column(db.Date, nullable=False)
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=False)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    supplier = db.relationship('Supplier', backref='medicines')
    sale_items = db.relationship('SaleItem', backref='medicine')

    def to_dict(self):
        """تحويل كائن Medicine إلى قاموس للتسلسل JSON"""
        return {
            'id': self.id,
            'name': self.name,
            'category': self.category,
            'price': self.price,
            'quantity': self.quantity,
            'expiry_date': self.expiry_date.isoformat() if self.expiry_date else None,
            'supplier_id': self.supplier_id,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'supplier_name': self.supplier.name if self.supplier else None
        }

class Customer(db.Model):
    __tablename__ = 'customers'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    phone = db.Column(db.String(20))
    email = db.Column(db.String(100))
    address = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    sales = db.relationship('Sale', backref='customer')

    def to_dict(self):
        """تحويل كائن Customer إلى قاموس للتسلسل JSON"""
        return {
            'id': self.id,
            'name': self.name,
            'phone': self.phone,
            'email': self.email,
            'address': self.address,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class Supplier(db.Model):
    __tablename__ = 'suppliers'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    phone = db.Column(db.String(20))
    email = db.Column(db.String(100))
    address = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        """تحويل كائن Supplier إلى قاموس للتسلسل JSON"""
        return {
            'id': self.id,
            'name': self.name,
            'phone': self.phone,
            'email': self.email,
            'address': self.address,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class Sale(db.Model):
    __tablename__ = 'sales'
    
    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'))
    total_amount = db.Column(db.Float, nullable=False)
    discount = db.Column(db.Float, default=0)
    final_amount = db.Column(db.Float, nullable=False)
    sale_date = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    sale_items = db.relationship('SaleItem', backref='sale', cascade='all, delete-orphan')

class SaleItem(db.Model):
    __tablename__ = 'sale_items'
    
    id = db.Column(db.Integer, primary_key=True)
    sale_id = db.Column(db.Integer, db.ForeignKey('sales.id'), nullable=False)
    medicine_id = db.Column(db.Integer, db.ForeignKey('medicines.id'), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    unit_price = db.Column(db.Float, nullable=False)
    total_price = db.Column(db.Float, nullable=False)

# الصفحة الرئيسية
@app.route('/')
def index():
    # إحصائيات سريعة
    total_medicines = Medicine.query.count()
    total_customers = Customer.query.count()
    total_suppliers = Supplier.query.count()
    
    # الأدوية منتهية الصلاحية أو قريبة من الانتهاء
    from datetime import timedelta
    warning_date = date.today() + timedelta(days=30)
    expiring_medicines = Medicine.query.filter(Medicine.expiry_date <= warning_date).all()
    
    # الأدوية قليلة المخزون
    low_stock_medicines = Medicine.query.filter(Medicine.quantity <= 10).all()
    
    # مبيعات اليوم
    today = date.today()
    today_sales = Sale.query.filter(
        db.func.date(Sale.sale_date) == today
    ).all()
    today_revenue = sum(sale.final_amount for sale in today_sales)
    
    return render_template('index.html',
                         total_medicines=total_medicines,
                         total_customers=total_customers,
                         total_suppliers=total_suppliers,
                         expiring_medicines=expiring_medicines,
                         low_stock_medicines=low_stock_medicines,
                         today_sales_count=len(today_sales),
                         today_revenue=today_revenue)

# إدارة الأدوية
@app.route('/medicines')
def medicines():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    
    query = Medicine.query
    if search:
        query = query.filter(Medicine.name.contains(search))
    
    medicines = query.paginate(
        page=page, per_page=app.config['ITEMS_PER_PAGE'], error_out=False
    )
    
    return render_template('medicines.html', medicines=medicines, search=search)

@app.route('/medicines/add', methods=['GET', 'POST'])
def add_medicine():
    if request.method == 'POST':
        try:
            # التحقق من البيانات المطلوبة
            name = request.form.get('name', '').strip()
            category = request.form.get('category', '').strip()
            price_str = request.form.get('price', '').strip()
            quantity_str = request.form.get('quantity', '').strip()
            expiry_date_str = request.form.get('expiry_date', '').strip()
            supplier_id_str = request.form.get('supplier_id', '').strip()
            description = request.form.get('description', '').strip()
            
            # التحقق من وجود البيانات المطلوبة
            if not all([name, category, price_str, quantity_str, expiry_date_str, supplier_id_str]):
                flash('جميع الحقول المطلوبة يجب أن تكون مملوءة', 'error')
                raise ValueError('Missing required fields')
            
            # تحويل البيانات
            price = float(price_str)
            quantity = int(quantity_str)
            supplier_id = int(supplier_id_str)
            
            # تحويل التاريخ
            try:
                expiry_date = datetime.strptime(expiry_date_str, '%Y-%m-%d').date()
            except ValueError:
                flash('تاريخ انتهاء الصلاحية غير صحيح', 'error')
                raise ValueError('Invalid expiry date format')
            
            medicine = Medicine(
                name=name,
                category=category,
                price=price,
                quantity=quantity,
                expiry_date=expiry_date,
                supplier_id=supplier_id,
                description=description
            )
            
            db.session.add(medicine)
            db.session.commit()
            flash('تم إضافة الدواء بنجاح!', 'success')
            return redirect(url_for('medicines'))
            
        except ValueError as ve:
            db.session.rollback()
            # flash message already set above for specific errors
            if 'Missing required fields' not in str(ve) and 'Invalid expiry date format' not in str(ve):
                flash(f'خطأ في البيانات المدخلة: {str(ve)}', 'error')
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')
    
    suppliers = Supplier.query.all()
    return render_template('add_medicine.html', suppliers=suppliers)

@app.route('/medicines/edit/<int:id>', methods=['GET', 'POST'])
def edit_medicine(id):
    medicine = Medicine.query.get_or_404(id)
    
    if request.method == 'POST':
        try:
            # التحقق من البيانات المطلوبة
            name = request.form.get('name', '').strip()
            category = request.form.get('category', '').strip()
            price_str = request.form.get('price', '').strip()
            quantity_str = request.form.get('quantity', '').strip()
            expiry_date_str = request.form.get('expiry_date', '').strip()
            supplier_id_str = request.form.get('supplier_id', '').strip()
            description = request.form.get('description', '').strip()
            
            # التحقق من وجود البيانات المطلوبة
            if not all([name, category, price_str, quantity_str, expiry_date_str, supplier_id_str]):
                flash('جميع الحقول المطلوبة يجب أن تكون مملوءة', 'error')
                raise ValueError('Missing required fields')
            
            # تحويل البيانات
            price = float(price_str)
            quantity = int(quantity_str)
            supplier_id = int(supplier_id_str)
            
            # تحويل التاريخ
            try:
                expiry_date = datetime.strptime(expiry_date_str, '%Y-%m-%d').date()
            except ValueError:
                flash('تاريخ انتهاء الصلاحية غير صحيح', 'error')
                raise ValueError('Invalid expiry date format')
            
            medicine.name = name
            medicine.category = category
            medicine.price = price
            medicine.quantity = quantity
            medicine.expiry_date = expiry_date
            medicine.supplier_id = supplier_id
            medicine.description = description
            
            db.session.commit()
            flash('تم تحديث الدواء بنجاح!', 'success')
            return redirect(url_for('medicines'))
            
        except ValueError as ve:
            db.session.rollback()
            # flash message already set above for specific errors
            if 'Missing required fields' not in str(ve) and 'Invalid expiry date format' not in str(ve):
                flash(f'خطأ في البيانات المدخلة: {str(ve)}', 'error')
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')
    
    suppliers = Supplier.query.all()
    return render_template('edit_medicine.html', medicine=medicine, suppliers=suppliers)

@app.route('/medicines/delete/<int:id>')
def delete_medicine(id):
    medicine = Medicine.query.get_or_404(id)
    try:
        db.session.delete(medicine)
        db.session.commit()
        flash('تم حذف الدواء بنجاح!', 'success')
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
    
    return redirect(url_for('medicines'))

# إدارة العملاء
@app.route('/customers')
def customers():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    
    query = Customer.query
    if search:
        query = query.filter(Customer.name.contains(search))
    
    customers = query.paginate(
        page=page, per_page=app.config['ITEMS_PER_PAGE'], error_out=False
    )
    
    return render_template('customers.html', customers=customers, search=search)

@app.route('/customers/add', methods=['GET', 'POST'])
def add_customer():
    if request.method == 'POST':
        try:
            customer = Customer(
                name=request.form['name'],
                phone=request.form.get('phone', ''),
                email=request.form.get('email', ''),
                address=request.form.get('address', '')
            )
            
            db.session.add(customer)
            db.session.commit()
            flash('تم إضافة العميل بنجاح!', 'success')
            return redirect(url_for('customers'))
            
        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'error')
    
    return render_template('add_customer.html')

# إدارة الموردين
@app.route('/suppliers')
def suppliers():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    
    query = Supplier.query
    if search:
        query = query.filter(Supplier.name.contains(search))
    
    suppliers = query.paginate(
        page=page, per_page=app.config['ITEMS_PER_PAGE'], error_out=False
    )
    
    return render_template('suppliers.html', suppliers=suppliers, search=search)

@app.route('/suppliers/add', methods=['GET', 'POST'])
def add_supplier():
    if request.method == 'POST':
        try:
            supplier = Supplier(
                name=request.form['name'],
                phone=request.form.get('phone', ''),
                email=request.form.get('email', ''),
                address=request.form.get('address', '')
            )
            
            db.session.add(supplier)
            db.session.commit()
            flash('تم إضافة المورد بنجاح!', 'success')
            return redirect(url_for('suppliers'))
            
        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'error')
    
    return render_template('add_supplier.html')

# نظام المبيعات
@app.route('/sales')
def sales():
    page = request.args.get('page', 1, type=int)
    sales = Sale.query.order_by(Sale.sale_date.desc()).paginate(
        page=page, per_page=app.config['ITEMS_PER_PAGE'], error_out=False
    )
    
    return render_template('sales.html', sales=sales)

@app.route('/sales/new', methods=['GET', 'POST'])
def new_sale():
    if request.method == 'POST':
        try:
            # إنشاء فاتورة جديدة
            sale = Sale(
                customer_id=request.form.get('customer_id') or None,
                total_amount=0,
                discount=float(request.form.get('discount', 0)),
                final_amount=0
            )
            
            db.session.add(sale)
            db.session.flush()  # للحصول على ID
            
            # إضافة عناصر الفاتورة
            items_data = json.loads(request.form['items'])
            total = 0
            
            for item in items_data:
                medicine = Medicine.query.get(item['medicine_id'])
                if medicine.quantity < item['quantity']:
                    raise Exception(f'المخزون غير كافي للدواء: {medicine.name}')
                
                sale_item = SaleItem(
                    sale_id=sale.id,
                    medicine_id=item['medicine_id'],
                    quantity=item['quantity'],
                    unit_price=medicine.price,
                    total_price=medicine.price * item['quantity']
                )
                
                # تحديث المخزون
                medicine.quantity -= item['quantity']
                
                db.session.add(sale_item)
                total += sale_item.total_price
            
            # تحديث إجمالي الفاتورة
            sale.total_amount = total
            sale.final_amount = total - sale.discount
            
            db.session.commit()
            flash('تم إنشاء الفاتورة بنجاح!', 'success')
            return redirect(url_for('sales'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')
    
    customers = Customer.query.all()
    medicines = Medicine.query.filter(Medicine.quantity > 0).all()
    # تحويل الأدوية إلى قواميس للاستخدام في JavaScript
    medicines_dict = [m.to_dict() for m in medicines]
    return render_template('new_sale.html', customers=customers, medicines=medicines_dict)

# API للبحث عن الأدوية
@app.route('/api/medicines/search')
def search_medicines():
    query = request.args.get('q', '')
    medicines = Medicine.query.filter(
        Medicine.name.contains(query),
        Medicine.quantity > 0
    ).limit(10).all()

    # استخدام to_dict() لتحويل الكائنات إلى قواميس
    return jsonify([m.to_dict() for m in medicines])

# التقارير
@app.route('/reports')
def reports():
    # إحصائيات سريعة
    total_medicines = Medicine.query.count()
    total_customers = Customer.query.count()
    total_suppliers = Supplier.query.count()
    total_sales = Sale.query.count()
    
    # إجمالي الإيرادات
    all_sales = Sale.query.all()
    total_revenue = sum(sale.final_amount for sale in all_sales)
    
    # إيرادات اليوم
    today = date.today()
    today_sales = Sale.query.filter(
        db.func.date(Sale.sale_date) == today
    ).all()
    today_revenue = sum(sale.final_amount for sale in today_sales)
    
    # إيرادات الشهر
    first_day_of_month = today.replace(day=1)
    monthly_sales = Sale.query.filter(
        Sale.sale_date >= first_day_of_month
    ).all()
    monthly_revenue = sum(sale.final_amount for sale in monthly_sales)
    
    # تنبيهات
    warning_date = today + timedelta(days=30)
    low_stock_count = Medicine.query.filter(Medicine.quantity <= 10).count()
    expired_count = Medicine.query.filter(Medicine.expiry_date < today).count()
    
    return render_template('reports.html',
                         total_medicines=total_medicines,
                         total_customers=total_customers,
                         total_suppliers=total_suppliers,
                         total_sales=total_sales,
                         total_revenue=total_revenue,
                         today_revenue=today_revenue,
                         monthly_sales=len(monthly_sales),
                         monthly_revenue=monthly_revenue,
                         low_stock_count=low_stock_count,
                         expired_count=expired_count)

@app.route('/reports/sales')
def sales_report():
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    days = request.args.get('days', type=int)
    
    query = Sale.query
    
    # إذا تم تحديد عدد الأيام
    if days:
        start_date_obj = date.today() - timedelta(days=days)
        query = query.filter(Sale.sale_date >= start_date_obj)
        start_date = start_date_obj.strftime('%Y-%m-%d')
        end_date = date.today().strftime('%Y-%m-%d')
    else:
        # إذا تم تحديد التواريخ يدوياً
        if start_date:
            query = query.filter(Sale.sale_date >= datetime.strptime(start_date, '%Y-%m-%d'))
        if end_date:
            query = query.filter(Sale.sale_date <= datetime.strptime(end_date, '%Y-%m-%d'))
    
    sales = query.order_by(Sale.sale_date.desc()).all()
    total_revenue = sum(sale.final_amount for sale in sales)
    
    return render_template('sales_report.html', 
                         sales=sales, 
                         total_revenue=total_revenue,
                         start_date=start_date,
                         end_date=end_date)

@app.route('/reports/inventory')
def inventory_report():
    medicines = Medicine.query.order_by(Medicine.name).all()
    total_value = sum(m.price * m.quantity for m in medicines)
    
    return render_template('inventory_report.html', 
                         medicines=medicines,
                         total_value=total_value)

# وظائف إضافية مفقودة
@app.route('/customers/edit/<int:id>', methods=['GET', 'POST'])
def edit_customer(id):
    customer = Customer.query.get_or_404(id)
    
    if request.method == 'POST':
        try:
            customer.name = request.form['name']
            customer.phone = request.form.get('phone', '')
            customer.email = request.form.get('email', '')
            customer.address = request.form.get('address', '')
            
            db.session.commit()
            flash('تم تحديث العميل بنجاح!', 'success')
            return redirect(url_for('customers'))
            
        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'error')
    
    return render_template('edit_customer.html', customer=customer)

@app.route('/customers/delete/<int:id>')
def delete_customer(id):
    customer = Customer.query.get_or_404(id)
    try:
        db.session.delete(customer)
        db.session.commit()
        flash('تم حذف العميل بنجاح!', 'success')
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
    
    return redirect(url_for('customers'))

@app.route('/suppliers/edit/<int:id>', methods=['GET', 'POST'])
def edit_supplier(id):
    supplier = Supplier.query.get_or_404(id)
    
    if request.method == 'POST':
        try:
            supplier.name = request.form['name']
            supplier.phone = request.form.get('phone', '')
            supplier.email = request.form.get('email', '')
            supplier.address = request.form.get('address', '')
            
            db.session.commit()
            flash('تم تحديث المورد بنجاح!', 'success')
            return redirect(url_for('suppliers'))
            
        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'error')
    
    return render_template('edit_supplier.html', supplier=supplier)

@app.route('/suppliers/delete/<int:id>')
def delete_supplier(id):
    supplier = Supplier.query.get_or_404(id)
    try:
        db.session.delete(supplier)
        db.session.commit()
        flash('تم حذف المورد بنجاح!', 'success')
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
    
    return redirect(url_for('suppliers'))

@app.route('/sales/delete/<int:id>')
def delete_sale(id):
    sale = Sale.query.get_or_404(id)
    try:
        # إرجاع الكميات للمخزون
        for item in sale.sale_items:
            medicine = Medicine.query.get(item.medicine_id)
            if medicine:
                medicine.quantity += item.quantity
        
        db.session.delete(sale)
        db.session.commit()
        flash('تم حذف الفاتورة بنجاح!', 'success')
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
    
    return redirect(url_for('sales'))

@app.route('/reports/sales_report')
def sales_report_page():
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    query = Sale.query
    if start_date:
        query = query.filter(Sale.sale_date >= datetime.strptime(start_date, '%Y-%m-%d'))
    if end_date:
        query = query.filter(Sale.sale_date <= datetime.strptime(end_date, '%Y-%m-%d'))
    
    sales = query.order_by(Sale.sale_date.desc()).all()
    total_revenue = sum(sale.final_amount for sale in sales)
    
    return render_template('sales_report.html', 
                         sales=sales, 
                         total_revenue=total_revenue,
                         start_date=start_date,
                         end_date=end_date)

@app.route('/reports/inventory_report')
def inventory_report_page():
    filter_type = request.args.get('filter')
    
    query = Medicine.query
    
    if filter_type == 'low_stock':
        query = query.filter(Medicine.quantity <= 10)
    elif filter_type == 'expired':
        query = query.filter(Medicine.expiry_date < date.today())
    elif filter_type == 'expiring':
        warning_date = date.today() + timedelta(days=30)
        query = query.filter(Medicine.expiry_date <= warning_date, Medicine.expiry_date >= date.today())
    
    medicines = query.order_by(Medicine.name).all()
    total_value = sum(m.price * m.quantity for m in medicines)
    
    return render_template('inventory_report.html', 
                         medicines=medicines,
                         total_value=total_value,
                         filter_type=filter_type)

@app.route('/reports/customers_report')
def customers_report():
    filter_type = request.args.get('filter')
    
    query = Customer.query
    
    if filter_type == 'active':
        query = query.join(Sale).distinct()
    elif filter_type == 'top':
        # أفضل العملاء حسب إجمالي المشتريات
        query = query.join(Sale).group_by(Customer.id).order_by(db.func.sum(Sale.final_amount).desc())
    
    customers = query.all()
    
    return render_template('customers_report.html', 
                         customers=customers,
                         filter_type=filter_type)

@app.route('/reports/suppliers_report')
def suppliers_report():
    filter_type = request.args.get('filter')
    
    query = Supplier.query
    
    if filter_type == 'active':
        query = query.join(Medicine).distinct()
    elif filter_type == 'medicines_count':
        query = query.join(Medicine).group_by(Supplier.id).order_by(db.func.count(Medicine.id).desc())
    
    suppliers = query.all()
    
    return render_template('suppliers_report.html', 
                         suppliers=suppliers,
                         filter_type=filter_type)

# إضافة context processor للتواريخ
@app.context_processor
def utility_processor():
    def format_datetime(dt):
        return dt.strftime('%Y-%m-%d %H:%M') if dt else ''
    
    def format_date(d):
        return d.strftime('%Y-%m-%d') if d else ''
    
    def now():
        return datetime.now()
    
    def today():
        return date.today()
    
    return dict(
        format_datetime=format_datetime,
        format_date=format_date,
        moment=now,
        now=now,
        today=today,
        datetime=datetime,
        timedelta=timedelta,
        date=date
    )

# إنشاء الجداول
with app.app_context():
    db.create_all()

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)